# Development Dockerfile for Svelte 5 + SvelteKit + shadcn-svelte app
FROM node:22-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies (including dev dependencies for development)
RUN npm ci

# Copy source code
COPY . .

# Create a non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S svelte -u 1001 -G nodejs && \
    chown -R svelte:nodejs /app

USER svelte

# Expose the development server port
EXPOSE 5173

# Start the development server with host binding for Docker
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "5173"]
