import log from 'loglevel';
import { browser, dev } from '$app/environment';
import { getLoggingConfig } from './config/logging.js';

/**
 * Log levels available in the application
 */
export const LOG_LEVELS = {
	TRACE: 0,
	DEBUG: 1,
	INFO: 2,
	WARN: 3,
	ERROR: 4,
	SILENT: 5
} as const;

export type LogLevel = keyof typeof LOG_LEVELS;

/**
 * Configuration for the logger
 */
interface LoggerConfig {
	level: LogLevel;
	prefix?: string;
	enableTimestamp?: boolean;
	enableColors?: boolean;
	persistLevel?: boolean;
}

/**
 * Get default logger configuration based on environment
 */
function getDefaultConfig(): LoggerConfig {
	const envConfig = getLoggingConfig();
	return {
		level: envConfig.level,
		prefix: '',
		enableTimestamp: envConfig.enableTimestamp,
		enableColors: envConfig.enableColors,
		persistLevel: envConfig.persistLevel
	};
}

/**
 * Enhanced logger class that wraps loglevel with additional features
 */
class Logger {
	private config: LoggerConfig;
	private originalFactory: typeof log.methodFactory;

	constructor(config: Partial<LoggerConfig> = {}) {
		this.config = { ...getDefaultConfig(), ...config };
		this.originalFactory = log.methodFactory;
		this.setupLogger();
	}

	/**
	 * Setup the logger with custom formatting and configuration
	 */
	private setupLogger(): void {
		// Set the log level
		log.setLevel(this.config.level.toLowerCase() as any, this.config.persistLevel);

		// Only customize formatting in browser environment
		if (browser) {
			log.methodFactory = (methodName, logLevel, loggerName) => {
				const rawMethod = this.originalFactory(methodName, logLevel, loggerName);
				
				return (...args: any[]) => {
					const timestamp = this.config.enableTimestamp ? this.getTimestamp() : '';
					const prefix = this.config.prefix ? `[${this.config.prefix}]` : '';
					const level = methodName.toUpperCase();
					
					// Format the log message
					const parts = [timestamp, prefix, `[${level}]`].filter(Boolean);
					const formattedPrefix = parts.length > 0 ? parts.join(' ') + ' ' : '';
					
					// Apply colors if enabled and in development
					if (this.config.enableColors && dev) {
						const coloredPrefix = this.colorizePrefix(formattedPrefix, methodName);
						rawMethod(coloredPrefix, ...args);
					} else {
						rawMethod(formattedPrefix, ...args);
					}
				};
			};
			
			// Rebuild the logger to apply the new method factory
			log.rebuild();
		}
	}

	/**
	 * Get formatted timestamp
	 */
	private getTimestamp(): string {
		return new Date().toISOString().substring(11, 23); // HH:mm:ss.SSS
	}

	/**
	 * Colorize the prefix based on log level
	 */
	private colorizePrefix(prefix: string, methodName: string): string {
		const colors = {
			trace: 'color: #6b7280', // gray
			debug: 'color: #3b82f6', // blue
			info: 'color: #10b981',  // green
			warn: 'color: #f59e0b',  // yellow
			error: 'color: #ef4444'  // red
		};
		
		const color = colors[methodName as keyof typeof colors] || colors.info;
		return `%c${prefix}`;
	}

	/**
	 * Set the log level
	 */
	setLevel(level: LogLevel): void {
		this.config.level = level;
		log.setLevel(level.toLowerCase() as any, this.config.persistLevel);
	}

	/**
	 * Get the current log level
	 */
	getLevel(): LogLevel {
		const numericLevel = log.getLevel();
		const levelEntries = Object.entries(LOG_LEVELS);
		const levelEntry = levelEntries.find(([, value]) => value === numericLevel);
		return (levelEntry?.[0] as LogLevel) || 'INFO';
	}

	/**
	 * Create a child logger with a prefix
	 */
	child(prefix: string): Logger {
		return new Logger({
			...this.config,
			prefix: this.config.prefix ? `${this.config.prefix}:${prefix}` : prefix
		});
	}

	/**
	 * Enable all logging (set to TRACE level)
	 */
	enableAll(): void {
		this.setLevel('TRACE');
	}

	/**
	 * Disable all logging (set to SILENT level)
	 */
	disableAll(): void {
		this.setLevel('SILENT');
	}

	// Logging methods
	trace(...args: any[]): void {
		log.trace(...args);
	}

	debug(...args: any[]): void {
		log.debug(...args);
	}

	info(...args: any[]): void {
		log.info(...args);
	}

	warn(...args: any[]): void {
		log.warn(...args);
	}

	error(...args: any[]): void {
		log.error(...args);
	}

	/**
	 * Log an error with stack trace
	 */
	exception(error: Error, ...args: any[]): void {
		log.error('Exception occurred:', error.message, ...args);
		if (error.stack && (dev || this.getLevel() === 'TRACE')) {
			log.error('Stack trace:', error.stack);
		}
	}

	/**
	 * Log performance timing
	 */
	time(label: string): void {
		if (browser && console.time) {
			console.time(label);
		}
	}

	timeEnd(label: string): void {
		if (browser && console.timeEnd) {
			console.timeEnd(label);
		}
	}

	/**
	 * Log a group of related messages
	 */
	group(label: string): void {
		if (browser && console.group) {
			console.group(label);
		}
	}

	groupEnd(): void {
		if (browser && console.groupEnd) {
			console.groupEnd();
		}
	}
}

/**
 * Default logger instance
 */
export const logger = new Logger();

/**
 * Create a new logger instance with custom configuration
 */
export function createLogger(config: Partial<LoggerConfig> = {}): Logger {
	return new Logger(config);
}

/**
 * Convenience function to create a child logger
 */
export function getLogger(name: string): Logger {
	return logger.child(name);
}

// Export log levels for external use
export { LOG_LEVELS as LogLevel };
