import log from 'loglevel';
import { browser, dev } from '$app/environment';
import { getLoggingConfig } from './config/logging.js';

/**
 * Log levels available in the application
 */
export const LOG_LEVELS = {
	TRACE: 0,
	DEBUG: 1,
	INFO: 2,
	WARN: 3,
	ERROR: 4,
	SILENT: 5
} as const;

export type LogLevel = keyof typeof LOG_LEVELS;

/**
 * Counter for creating unique logger instances
 */
let loggerCounter = 0;

/**
 * Cache for named logger instances
 */
const loggerCache = new Map<string, Logger>();

/**
 * Configuration for the logger
 */
interface LoggerConfig {
	level: LogLevel;
	prefix?: string;
	enableTimestamp?: boolean;
	enableColors?: boolean;
	persistLevel?: boolean;
}

/**
 * Get default logger configuration based on environment
 */
function getDefaultConfig(): LoggerConfig {
	const envConfig = getLoggingConfig();
	return {
		level: envConfig.level,
		prefix: '',
		enableTimestamp: envConfig.enableTimestamp,
		enableColors: envConfig.enableColors,
		persistLevel: envConfig.persistLevel
	};
}

/**
 * Enhanced logger class that wraps loglevel with additional features
 */
class Logger {
	private config: LoggerConfig;
	private logInstance: ReturnType<typeof log.getLogger>;
	private originalFactory: typeof log.methodFactory;

	constructor(config: Partial<LoggerConfig> = {}) {
		this.config = { ...getDefaultConfig(), ...config };
		// Create a unique logger instance for this Logger
		const loggerName = this.config.prefix || `logger-${++loggerCounter}`;
		this.logInstance = log.getLogger(loggerName);
		this.originalFactory = this.logInstance.methodFactory;
		this.setupLogger();
	}

	/**
	 * Setup the logger with custom formatting and configuration
	 */
	private setupLogger(): void {
		// Set the log level on our specific instance
		this.logInstance.setLevel(this.config.level.toLowerCase() as any, this.config.persistLevel);

		// Only customize formatting in browser environment
		if (browser) {
			// Store reference to this logger instance for the method factory
			const config = this.config;
			const getTimestamp = this.getTimestamp.bind(this);
			const getColorForLevel = this.getColorForLevel.bind(this);

			this.logInstance.methodFactory = (
				methodName: string,
				logLevel: number,
				loggerName: string | symbol
			) => {
				const rawMethod = this.originalFactory(methodName as any, logLevel, loggerName);

				return (...args: unknown[]) => {
					const timestamp = config.enableTimestamp ? getTimestamp() : '';
					const prefix = config.prefix ? `[${config.prefix}]` : '';
					const level = methodName.toUpperCase();

					// Format the log message
					const parts = [timestamp, prefix, `[${level}]`].filter(Boolean);
					const formattedPrefix = parts.length > 0 ? parts.join(' ') + ' ' : '';

					// Apply colors if enabled and in development
					if (config.enableColors && dev) {
						const color = getColorForLevel(methodName);
						rawMethod(`%c${formattedPrefix}`, `color: ${color}`, ...args);
					} else {
						rawMethod(formattedPrefix, ...args);
					}
				};
			};

			// Rebuild this specific logger instance
			this.logInstance.rebuild();
		}
	}

	/**
	 * Get formatted timestamp
	 */
	private getTimestamp(): string {
		return new Date().toISOString().substring(11, 23); // HH:mm:ss.SSS
	}

	/**
	 * Get color for log level
	 */
	private getColorForLevel(methodName: string): string {
		const colors = {
			trace: '#6b7280', // gray
			debug: '#3b82f6', // blue
			info: '#10b981', // green
			warn: '#f59e0b', // yellow
			error: '#ef4444' // red
		};

		return colors[methodName as keyof typeof colors] || colors.info;
	}

	/**
	 * Set the log level
	 */
	setLevel(level: LogLevel): void {
		this.config.level = level;
		this.logInstance.setLevel(level.toLowerCase() as 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'silent', this.config.persistLevel);
	}

	/**
	 * Get the current log level
	 */
	getLevel(): LogLevel {
		const numericLevel = this.logInstance.getLevel();
		const levelEntries = Object.entries(LOG_LEVELS);
		const levelEntry = levelEntries.find(([, value]) => value === numericLevel);
		return (levelEntry?.[0] as LogLevel) || 'INFO';
	}

	/**
	 * Create a child logger with a prefix
	 */
	child(prefix: string): Logger {
		return new Logger({
			...this.config,
			prefix: this.config.prefix ? `${this.config.prefix}:${prefix}` : prefix
		});
	}

	/**
	 * Enable all logging (set to TRACE level)
	 */
	enableAll(): void {
		this.setLevel('TRACE');
	}

	/**
	 * Disable all logging (set to SILENT level)
	 */
	disableAll(): void {
		this.setLevel('SILENT');
	}

	// Logging methods
	trace(...args: unknown[]): void {
		this.logInstance.trace(...args);
	}

	debug(...args: unknown[]): void {
		this.logInstance.debug(...args);
	}

	info(...args: unknown[]): void {
		this.logInstance.info(...args);
	}

	warn(...args: unknown[]): void {
		this.logInstance.warn(...args);
	}

	error(...args: unknown[]): void {
		this.logInstance.error(...args);
	}

	/**
	 * Log an error with stack trace
	 */
	exception(error: Error, ...args: unknown[]): void {
		this.logInstance.error('Exception occurred:', error.message, ...args);
		if (error.stack && (dev || this.getLevel() === 'TRACE')) {
			this.logInstance.error('Stack trace:', error.stack);
		}
	}

	/**
	 * Log performance timing
	 */
	time(label: string): void {
		if (browser && console.time) {
			console.time(label);
		}
	}

	timeEnd(label: string): void {
		if (browser && console.timeEnd) {
			console.timeEnd(label);
		}
	}

	/**
	 * Log a group of related messages
	 */
	group(label: string): void {
		if (browser && console.group) {
			console.group(label);
		}
	}

	groupEnd(): void {
		if (browser && console.groupEnd) {
			console.groupEnd();
		}
	}
}

/**
 * Default logger instance
 */
export const logger = new Logger();

/**
 * Create a new logger instance with custom configuration
 */
export function createLogger(config: Partial<LoggerConfig> = {}): Logger {
	return new Logger(config);
}

/**
 * Convenience function to create a child logger
 */
export function getLogger(name: string): Logger {
	if (loggerCache.has(name)) {
		return loggerCache.get(name)!;
	}

	const newLogger = new Logger({ prefix: name });
	loggerCache.set(name, newLogger);
	return newLogger;
}

// Export log levels for external use
export { LOG_LEVELS as LogLevel };

/**
 * Debug function to test logging in browser console
 * Usage: testLogging() in browser console
 */
if (browser && typeof window !== 'undefined') {
	(window as unknown as { testLogging: () => void }).testLogging = () => {
		console.log('=== Testing Logger ===');
		logger.trace('This is a TRACE message');
		logger.debug('This is a DEBUG message');
		logger.info('This is an INFO message');
		logger.warn('This is a WARN message');
		logger.error('This is an ERROR message');

		const testLogger = getLogger('TEST');
		testLogger.info('This is from a specialized logger');

		console.log('Current log level:', logger.getLevel());
		console.log('=== Test Complete ===');
	};
}
