import { dev, browser } from '$app/environment';
import type { LogLevel } from '../logger.js';

/**
 * Logging configuration for different environments
 */
export interface LoggingConfig {
	level: LogLevel;
	enableTimestamp: boolean;
	enableColors: boolean;
	persistLevel: boolean;
	enablePerformanceLogging: boolean;
	enableGrouping: boolean;
}

/**
 * Development environment logging configuration
 */
const developmentConfig: LoggingConfig = {
	level: 'DEBUG',
	enableTimestamp: true,
	enableColors: true,
	persistLevel: true,
	enablePerformanceLogging: true,
	enableGrouping: true
};

/**
 * Production environment logging configuration
 */
const productionConfig: LoggingConfig = {
	level: 'WARN',
	enableTimestamp: false,
	enableColors: false,
	persistLevel: false,
	enablePerformanceLogging: false,
	enableGrouping: false
};

/**
 * Test environment logging configuration
 */
const testConfig: LoggingConfig = {
	level: 'ERROR',
	enableTimestamp: false,
	enableColors: false,
	persistLevel: false,
	enablePerformanceLogging: false,
	enableGrouping: false
};

/**
 * Get the appropriate logging configuration based on the current environment
 */
export function getLoggingConfig(): LoggingConfig {
	// Check for environment-specific overrides
	if (browser && typeof window !== 'undefined') {
		// Check for URL parameters that override log level
		const urlParams = new URLSearchParams(window.location.search);
		const urlLogLevel = urlParams.get('logLevel');

		if (urlLogLevel && isValidLogLevel(urlLogLevel)) {
			return {
				...getBaseConfig(),
				level: urlLogLevel as LogLevel
			};
		}

		// Check for localStorage override
		const storedLevel = localStorage.getItem('loglevel:webpack-dev-server');
		if (storedLevel && isValidLogLevel(storedLevel)) {
			return {
				...getBaseConfig(),
				level: storedLevel as LogLevel
			};
		}
	}

	return getBaseConfig();
}

/**
 * Get base configuration for current environment
 */
function getBaseConfig(): LoggingConfig {
	if (dev) {
		return developmentConfig;
	}

	// Check if we're in a test environment
	if (browser && typeof window !== 'undefined' && window.location.hostname === 'localhost') {
		// Additional check for test environment indicators
		if (window.location.port === '4173' || window.location.search.includes('test=true')) {
			return testConfig;
		}
	}

	return productionConfig;
}

/**
 * Check if a string is a valid log level
 */
function isValidLogLevel(level: string): boolean {
	const validLevels = ['TRACE', 'DEBUG', 'INFO', 'WARN', 'ERROR', 'SILENT'];
	return validLevels.includes(level.toUpperCase());
}

/**
 * Logging best practices and guidelines
 */
export const LOGGING_GUIDELINES = {
	/**
	 * When to use each log level:
	 *
	 * TRACE: Very detailed information, typically only of interest when diagnosing problems.
	 * DEBUG: Detailed information, typically only of interest when diagnosing problems.
	 * INFO: General information about application flow.
	 * WARN: Potentially harmful situations that should be noted.
	 * ERROR: Error events that might still allow the application to continue running.
	 */
	levels: {
		TRACE: 'Very detailed debugging information',
		DEBUG: 'General debugging information',
		INFO: 'General application flow information',
		WARN: 'Potentially harmful situations',
		ERROR: 'Error events that need attention'
	},

	/**
	 * Best practices for logging:
	 */
	bestPractices: [
		'Use structured logging with consistent message formats',
		'Include relevant context in log messages',
		'Avoid logging sensitive information (passwords, tokens, etc.)',
		'Use appropriate log levels for different types of messages',
		'Create specialized loggers for different application modules',
		'Use performance logging for monitoring slow operations',
		'Group related log messages when appropriate',
		'Handle errors gracefully with proper exception logging'
	],

	/**
	 * Examples of good log messages:
	 */
	examples: {
		good: [
			'User authentication successful for user ID: 12345',
			'API request to /users completed in 245ms',
			'Failed to load user preferences: Network timeout after 30s',
			'Cache miss for key "user:12345", fetching from database'
		],
		bad: ['Success', 'Error occurred', 'User logged in', 'Something went wrong']
	}
} as const;
