/**
 * Logger-related type definitions
 */

export interface LogContext {
	timestamp?: string;
	level?: string;
	prefix?: string;
	[key: string]: any;
}

export interface LogEntry {
	level: string;
	message: string;
	timestamp: string;
	context?: LogContext;
	error?: Error;
}

export interface LoggerMetrics {
	totalLogs: number;
	errorCount: number;
	warnCount: number;
	lastLogTime: string;
}

export type LogMethod = (...args: any[]) => void;

export interface ILogger {
	trace: LogMethod;
	debug: LogMethod;
	info: LogMethod;
	warn: LogMethod;
	error: LogMethod;
	exception: (error: Error, ...args: any[]) => void;
	time: (label: string) => void;
	timeEnd: (label: string) => void;
	group: (label: string) => void;
	groupEnd: () => void;
	setLevel: (level: string) => void;
	getLevel: () => string;
	child: (prefix: string) => ILogger;
	enableAll: () => void;
	disableAll: () => void;
}
