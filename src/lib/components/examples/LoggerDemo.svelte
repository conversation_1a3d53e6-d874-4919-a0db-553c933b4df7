<script lang="ts">
	import { logger, getLogger, LOG_LEVELS, type LogLevel } from '$lib';
	import { Button } from '$lib/components/ui/button/index.js';
	import { onMount } from 'svelte';

	// Create specialized loggers for different parts of the application
	const uiLogger = getLogger('UI');
	const apiLogger = getLogger('API');
	const authLogger = getLogger('Auth');

	let currentLevel: LogLevel = logger.getLevel();
	let logCount = 0;

	onMount(() => {
		logger.info('Logger demo component mounted');
	});

	function testAllLogLevels() {
		logCount++;
		const testId = `test-${logCount}`;

		logger.trace(`[${testId}] This is a trace message - very detailed debugging info`);
		logger.debug(`[${testId}] This is a debug message - general debugging info`);
		logger.info(`[${testId}] This is an info message - general information`);
		logger.warn(`[${testId}] This is a warning message - something might be wrong`);
		logger.error(`[${testId}] This is an error message - something went wrong`);
	}

	function testSpecializedLoggers() {
		uiLogger.info('User clicked a button');
		uiLogger.debug('Component state updated');

		apiLogger.info('Making API request to /users');
		apiLogger.warn('API response took longer than expected (2.5s)');

		authLogger.info('User authentication successful');
		authLogger.debug('JWT token refreshed');
	}

	function testErrorLogging() {
		try {
			// Simulate an error
			throw new Error('This is a simulated error for demonstration');
		} catch (error) {
			logger.exception(error as Error, 'Additional context about the error');
		}
	}

	function testPerformanceLogging() {
		logger.time('expensive-operation');

		// Simulate some work
		setTimeout(() => {
			logger.timeEnd('expensive-operation');
			logger.info('Performance test completed');
		}, 1000);
	}

	function testGroupedLogging() {
		logger.group('User Registration Process');
		logger.info('Step 1: Validating user input');
		logger.info('Step 2: Checking email availability');
		logger.info('Step 3: Creating user account');
		logger.info('Step 4: Sending welcome email');
		logger.groupEnd();
	}

	function changeLogLevel(level: LogLevel) {
		logger.setLevel(level);
		currentLevel = logger.getLevel();
		logger.info(`Log level changed to: ${level}`);
	}

	function enableAllLogs() {
		logger.enableAll();
		currentLevel = logger.getLevel();
		logger.trace('All logging enabled - you should see this trace message');
	}

	function disableAllLogs() {
		logger.disableAll();
		currentLevel = logger.getLevel();
		// This won't be visible since logging is disabled
		logger.error('This error message should not be visible');
	}

	const logLevels = Object.keys(LOG_LEVELS) as LogLevel[];
</script>

<div class="space-y-6 rounded-lg border p-6">
	<div class="space-y-2">
		<h3 class="text-lg font-semibold">Logger Demo</h3>
		<p class="text-sm text-gray-600 dark:text-gray-400">
			Test the standardized logging system. Open your browser's developer console to see the logs.
		</p>
		<p class="text-xs text-gray-500">
			Current log level: <span class="font-mono font-semibold">{currentLevel}</span>
		</p>
	</div>

	<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
		<!-- Basic Logging Tests -->
		<div class="space-y-3">
			<h4 class="font-medium">Basic Logging</h4>
			<div class="space-y-2">
				<Button on:click={testAllLogLevels} variant="outline" size="sm" class="w-full">
					Test All Log Levels
				</Button>
				<Button on:click={testSpecializedLoggers} variant="outline" size="sm" class="w-full">
					Test Specialized Loggers
				</Button>
				<Button on:click={testErrorLogging} variant="outline" size="sm" class="w-full">
					Test Error Logging
				</Button>
			</div>
		</div>

		<!-- Advanced Logging Tests -->
		<div class="space-y-3">
			<h4 class="font-medium">Advanced Features</h4>
			<div class="space-y-2">
				<Button on:click={testPerformanceLogging} variant="outline" size="sm" class="w-full">
					Test Performance Logging
				</Button>
				<Button on:click={testGroupedLogging} variant="outline" size="sm" class="w-full">
					Test Grouped Logging
				</Button>
			</div>
		</div>
	</div>

	<!-- Log Level Controls -->
	<div class="space-y-3">
		<h4 class="font-medium">Log Level Controls</h4>
		<div class="flex flex-wrap gap-2">
			{#each logLevels as level}
				<Button
					on:click={() => changeLogLevel(level)}
					variant={currentLevel === level ? 'default' : 'outline'}
					size="sm"
				>
					{level}
				</Button>
			{/each}
		</div>
		<div class="flex gap-2">
			<Button on:click={enableAllLogs} variant="outline" size="sm">Enable All</Button>
			<Button on:click={disableAllLogs} variant="outline" size="sm">Disable All</Button>
		</div>
	</div>

	<!-- Usage Examples -->
	<div class="space-y-3">
		<h4 class="font-medium">Usage Examples</h4>
		<div class="rounded bg-gray-50 p-3 text-xs dark:bg-gray-800">
			<pre class="whitespace-pre-wrap"><code
					>{`// Import the logger
import { logger, getLogger } from '$lib';

// Basic logging
logger.info('Application started');
logger.warn('This is a warning');
logger.error('Something went wrong');

// Create specialized loggers
const apiLogger = getLogger('API');
apiLogger.debug('Making request to /api/users');

// Error handling with context
try {
  // some operation
} catch (error) {
  logger.exception(error, 'Failed to load user data');
}

// Performance monitoring
logger.time('data-processing');
// ... do work ...
logger.timeEnd('data-processing');`}</code
				></pre>
		</div>
	</div>
</div>
