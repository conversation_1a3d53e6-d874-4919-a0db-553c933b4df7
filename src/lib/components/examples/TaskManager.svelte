<script lang="ts" module>
	export interface Task {
		id: number;
		title: string;
		description: string;
		completed: boolean;
		priority: 'low' | 'medium' | 'high';
		createdAt: Date;
	}

	export type TaskFilter = 'all' | 'active' | 'completed';
</script>

<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { cn } from '$lib/utils.js';
	import { getLogger } from '$lib';
	import { onMount } from 'svelte';

	// Create a specialized logger for the TaskManager component
	const taskLogger = getLogger('TaskManager');

	onMount(() => {
		taskLogger.info('TaskManager component mounted');
		taskLogger.debug('Initial task count:', tasks.length);
	});

	// State management using Svelte 5 runes
	let tasks = $state<Task[]>([
		{
			id: 1,
			title: 'Setup Svelte 5 project',
			description: 'Initialize a new SvelteKit project with Svelte 5 and shadcn-svelte',
			completed: true,
			priority: 'high',
			createdAt: new Date('2024-01-15')
		},
		{
			id: 2,
			title: 'Create reusable components',
			description: 'Build maintainable components following best practices',
			completed: false,
			priority: 'high',
			createdAt: new Date('2024-01-16')
		},
		{
			id: 3,
			title: 'Add unit tests',
			description: 'Write comprehensive tests for all components',
			completed: false,
			priority: 'medium',
			createdAt: new Date('2024-01-17')
		}
	]);

	let newTaskTitle = $state('');
	let newTaskDescription = $state('');
	let newTaskPriority = $state<Task['priority']>('medium');
	let filter = $state<TaskFilter>('all');
	let isAddingTask = $state(false);

	// Derived state
	let filteredTasks = $derived.by(() => {
		switch (filter) {
			case 'active':
				return tasks.filter((task) => !task.completed);
			case 'completed':
				return tasks.filter((task) => task.completed);
			default:
				return tasks;
		}
	});

	let taskStats = $derived.by(() => {
		const total = tasks.length;
		const completed = tasks.filter((task) => task.completed).length;
		const active = total - completed;
		return { total, completed, active };
	});

	// Event handlers
	function addTask() {
		if (!newTaskTitle.trim()) {
			taskLogger.warn('Attempted to add task with empty title');
			return;
		}

		const newTask: Task = {
			id: Date.now(),
			title: newTaskTitle.trim(),
			description: newTaskDescription.trim(),
			completed: false,
			priority: newTaskPriority,
			createdAt: new Date()
		};

		tasks.push(newTask);
		taskLogger.info('Task added successfully', {
			taskId: newTask.id,
			title: newTask.title,
			priority: newTask.priority
		});
		taskLogger.debug('Total tasks after addition:', tasks.length);

		// Reset form
		newTaskTitle = '';
		newTaskDescription = '';
		newTaskPriority = 'medium';
		isAddingTask = false;
	}

	function toggleTask(taskId: number) {
		const task = tasks.find((t) => t.id === taskId);
		if (task) {
			const previousState = task.completed;
			task.completed = !task.completed;
			taskLogger.info('Task toggled', {
				taskId,
				title: task.title,
				from: previousState ? 'completed' : 'active',
				to: task.completed ? 'completed' : 'active'
			});
		} else {
			taskLogger.error('Attempted to toggle non-existent task', { taskId });
		}
	}

	function deleteTask(taskId: number) {
		tasks = tasks.filter((t) => t.id !== taskId);
	}

	function getPriorityColor(priority: Task['priority']) {
		switch (priority) {
			case 'high':
				return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
			case 'medium':
				return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
			case 'low':
				return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
		}
	}
</script>

<div
	class="rounded-lg border border-gray-200 bg-white p-6 shadow-md dark:border-gray-700 dark:bg-gray-800"
>
	<!-- Header with stats -->
	<div class="mb-6 flex items-center justify-between">
		<div>
			<h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Task Manager</h2>
			<p class="text-sm text-gray-600 dark:text-gray-400">
				{taskStats.total} total, {taskStats.active} active, {taskStats.completed} completed
			</p>
		</div>

		<Button variant="default" onclick={() => (isAddingTask = !isAddingTask)}>
			{isAddingTask ? 'Cancel' : 'Add Task'}
		</Button>
	</div>

	<!-- Add task form -->
	{#if isAddingTask}
		<div class="mb-6 rounded-lg bg-gray-50 p-4 dark:bg-gray-700">
			<div class="space-y-4">
				<div>
					<label
						for="task-title"
						class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
					>
						Title
					</label>
					<input
						id="task-title"
						type="text"
						bind:value={newTaskTitle}
						placeholder="Enter task title..."
						class="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100"
					/>
				</div>

				<div>
					<label
						for="task-description"
						class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
					>
						Description
					</label>
					<textarea
						id="task-description"
						bind:value={newTaskDescription}
						placeholder="Enter task description..."
						rows="2"
						class="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100"
					></textarea>
				</div>

				<div>
					<label
						for="task-priority"
						class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
					>
						Priority
					</label>
					<select
						id="task-priority"
						bind:value={newTaskPriority}
						class="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100"
					>
						<option value="low">Low</option>
						<option value="medium">Medium</option>
						<option value="high">High</option>
					</select>
				</div>

				<div class="flex space-x-2">
					<Button variant="default" onclick={addTask} disabled={!newTaskTitle.trim()}>
						Add Task
					</Button>
					<Button variant="outline" onclick={() => (isAddingTask = false)}>Cancel</Button>
				</div>
			</div>
		</div>
	{/if}

	<!-- Filter buttons -->
	<div class="mb-4 flex space-x-2">
		{#each ['all', 'active', 'completed'] as filterOption (filterOption)}
			<button
				class={cn(
					'rounded-md px-3 py-1 text-sm font-medium transition-colors',
					filter === filterOption
						? 'bg-blue-500 text-white'
						: 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
				)}
				onclick={() => (filter = filterOption as TaskFilter)}
			>
				{filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
			</button>
		{/each}
	</div>

	<!-- Task list -->
	<div class="space-y-3">
		{#each filteredTasks as task (task.id)}
			<div
				class={cn(
					'rounded-lg border border-gray-200 p-4 transition-all dark:border-gray-600',
					task.completed && 'opacity-60'
				)}
			>
				<div class="flex items-start space-x-3">
					<input
						type="checkbox"
						checked={task.completed}
						onchange={() => toggleTask(task.id)}
						class="mt-1 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
					/>

					<div class="min-w-0 flex-1">
						<h3
							class={cn(
								'text-sm font-medium',
								task.completed
									? 'text-gray-500 line-through dark:text-gray-400'
									: 'text-gray-900 dark:text-gray-100'
							)}
						>
							{task.title}
						</h3>

						{#if task.description}
							<p
								class={cn(
									'mt-1 text-sm',
									task.completed
										? 'text-gray-400 line-through dark:text-gray-500'
										: 'text-gray-600 dark:text-gray-400'
								)}
							>
								{task.description}
							</p>
						{/if}

						<div class="mt-2 flex items-center space-x-2">
							<span
								class={cn(
									'inline-flex items-center rounded px-2 py-0.5 text-xs font-medium',
									getPriorityColor(task.priority)
								)}
							>
								{task.priority}
							</span>
							<span class="text-xs text-gray-500 dark:text-gray-400">
								{task.createdAt.toLocaleDateString()}
							</span>
						</div>
					</div>

					<Button
						variant="outline"
						size="sm"
						onclick={() => deleteTask(task.id)}
						class="text-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:hover:text-red-300"
					>
						Delete
					</Button>
				</div>
			</div>
		{:else}
			<div class="text-center py-8 text-gray-500 dark:text-gray-400">
				{filter === 'all' ? 'No tasks yet' : `No ${filter} tasks`}
			</div>
		{/each}
	</div>
</div>
