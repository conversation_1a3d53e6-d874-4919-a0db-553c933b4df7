<script lang="ts" module>
	import type { HTMLAttributes } from 'svelte/elements';

	export interface User {
		id: number;
		name: string;
		email: string;
		avatar: string;
		role: string;
		isOnline: boolean;
	}

	export interface UserCardProps extends HTMLAttributes<HTMLDivElement> {
		user: User;
		onUserClick?: (user: User) => void;
	}
</script>

<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { Avatar, AvatarImage, AvatarFallback } from '$lib/components/ui/avatar/index.js';
	import { cn } from '$lib/utils.js';

	let { user, onUserClick, class: className, ...restProps }: UserCardProps = $props();

	// Reactive state using Svelte 5 runes
	let isHovered = $state(false);
	let isLoading = $state(false);

	// Derived state
	let statusColor = $derived(user.isOnline ? 'bg-green-500' : 'bg-gray-400');
	let statusText = $derived(user.isOnline ? 'Online' : 'Offline');

	// Event handlers
	function handleUserClick() {
		if (onUserClick) {
			onUserClick(user);
		}
	}

	async function handleSendMessage() {
		isLoading = true;
		// Simulate API call
		await new Promise((resolve) => setTimeout(resolve, 1000));
		isLoading = false;
		console.log(`Sending message to ${user.name}`);
	}
</script>

<div
	class={cn(
		'rounded-lg border border-gray-200 bg-white p-6 shadow-md transition-all duration-200 dark:border-gray-700 dark:bg-gray-800',
		'cursor-pointer hover:scale-[1.02] hover:shadow-lg',
		isHovered && 'ring-opacity-50 ring-2 ring-blue-500',
		className
	)}
	role="button"
	tabindex="0"
	onmouseenter={() => (isHovered = true)}
	onmouseleave={() => (isHovered = false)}
	onclick={handleUserClick}
	onkeydown={(e) => {
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			handleUserClick();
		}
	}}
	{...restProps}
>
	<!-- User Avatar and Status -->
	<div class="mb-4 flex items-center space-x-4">
		<div class="relative">
			<Avatar class="size-12">
				<AvatarImage src={user.avatar} alt={`${user.name}'s avatar`} />
				<AvatarFallback
					>{user.name
						.split(' ')
						.map((n) => n[0])
						.join('')}</AvatarFallback
				>
			</Avatar>
			<!-- Online status indicator -->
			<div
				class={cn(
					'absolute -right-1 -bottom-1 h-4 w-4 rounded-full border-2 border-white dark:border-gray-800',
					statusColor
				)}
				title={statusText}
			></div>
		</div>

		<div class="min-w-0 flex-1">
			<h3 class="truncate text-lg font-semibold text-gray-900 dark:text-gray-100">
				{user.name}
			</h3>
			<p class="truncate text-sm text-gray-600 dark:text-gray-400">
				{user.role}
			</p>
		</div>
	</div>

	<!-- User Email -->
	<div class="mb-4">
		<p class="truncate text-sm text-gray-500 dark:text-gray-400">
			{user.email}
		</p>
	</div>

	<!-- Status Badge -->
	<div class="mb-4">
		<span
			class={cn(
				'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium',
				user.isOnline
					? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
					: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
			)}
		>
			<span class={cn('mr-1.5 h-1.5 w-1.5 rounded-full', statusColor)}></span>
			{statusText}
		</span>
	</div>

	<!-- Action Buttons -->
	<div class="flex space-x-2">
		<Button
			variant="outline"
			size="sm"
			class="flex-1"
			onclick={(e) => {
				e.stopPropagation();
				console.log(`Viewing ${user.name}'s profile`);
			}}
		>
			View Profile
		</Button>

		<Button
			variant="default"
			size="sm"
			class="flex-1"
			disabled={isLoading}
			onclick={(e) => {
				e.stopPropagation();
				handleSendMessage();
			}}
		>
			{isLoading ? 'Sending...' : 'Message'}
		</Button>
	</div>
</div>
