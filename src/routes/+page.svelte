<script lang="ts">
	import FormExample from '$lib/components/examples/FormExample.svelte';
	import LoggerDemo from '$lib/components/examples/LoggerDemo.svelte';
	import TaskManager from '$lib/components/examples/TaskManager.svelte';
	import UserCard from '$lib/components/examples/UserCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { logger } from '$lib';
	import { onMount } from 'svelte';

	// This proves SSR is disabled - window is only available in browser
	let isClient = false;

	onMount(() => {
		isClient = true;
		logger.info('✅ SPA Mode: Running in browser only - SSR is completely disabled');
		logger.debug('Application mounted successfully');
	});

	// Example data for demonstration
	const users = [
		{
			id: 1,
			name: '<PERSON>',
			email: '<EMAIL>',
			avatar:
				'https://api.dicebear.com/7.x/avataaars/svg?seed=Alice&backgroundColor=b6e3f4&clothesColor=262e33&eyebrowType=default&eyeType=happy&facialHairColor=auburn&facialHairType=blank&hairColor=auburn&hatColor=black&mouthType=smile&skinColor=light&topType=longHairStraight',
			role: 'Frontend Developer',
			isOnline: true
		},
		{
			id: 2,
			name: 'Bob Smith',
			email: '<EMAIL>',
			avatar:
				'https://api.dicebear.com/7.x/avataaars/svg?seed=Bob&backgroundColor=c0aede&clothesColor=3c4f5c&eyebrowType=default&eyeType=default&facialHairColor=brown&facialHairType=moustacheFancy&hairColor=brown&hatColor=black&mouthType=default&skinColor=light&topType=shortHairShortFlat',
			role: 'Backend Developer',
			isOnline: false
		},
		{
			id: 3,
			name: 'Carol Davis',
			email: '<EMAIL>',
			avatar:
				'https://api.dicebear.com/7.x/avataaars/svg?seed=Carol&backgroundColor=ffd93d&clothesColor=ff488e&eyebrowType=default&eyeType=happy&facialHairColor=blonde&facialHairType=blank&hairColor=blonde&hatColor=black&mouthType=smile&skinColor=light&topType=longHairCurly',
			role: 'UI/UX Designer',
			isOnline: true
		}
	];
</script>

<div class="container mx-auto space-y-12 p-8">
	<header class="space-y-4 text-center">
		<h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100">
			Svelte 5 + SvelteKit + shadcn-svelte
		</h1>
		<p class="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-400">
			A maintainable frontend component library built with the latest technologies and best
			practices.
		</p>

		<!-- SPA Status Indicator -->
		{#if isClient}
			<div class="mx-auto max-w-md rounded-lg border border-green-200 bg-green-50 p-4">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
							<path
								fill-rule="evenodd"
								d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
								clip-rule="evenodd"
							/>
						</svg>
					</div>
					<div class="ml-3">
						<p class="text-sm font-medium text-green-800">
							✅ SPA Mode Active - SSR Completely Disabled
						</p>
						<p class="mt-1 text-xs text-green-600">
							Running in browser only • No server-side rendering
						</p>
					</div>
				</div>
			</div>
		{:else}
			<div class="mx-auto max-w-md rounded-lg border border-yellow-200 bg-yellow-50 p-4">
				<p class="text-sm text-yellow-800">Loading SPA...</p>
			</div>
		{/if}

		<div class="flex justify-center gap-4">
			<Button variant="default">Get Started</Button>
			<Button variant="outline">View Documentation</Button>
		</div>
	</header>

	<section class="space-y-6">
		<h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Example Components</h2>

		<!-- User Cards Example -->
		<div class="space-y-4">
			<h3 class="text-xl font-medium text-gray-800 dark:text-gray-200">User Cards</h3>
			<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
				{#each users as user (user.id)}
					<UserCard {user} />
				{/each}
			</div>
		</div>

		<!-- Task Manager Example -->
		<div class="space-y-4">
			<h3 class="text-xl font-medium text-gray-800 dark:text-gray-200">Task Manager</h3>
			<TaskManager />
		</div>

		<!-- Form Example -->
		<div class="space-y-4">
			<h3 class="text-xl font-medium text-gray-800 dark:text-gray-200">Form Example</h3>
			<FormExample />
		</div>

		<!-- Logger Demo -->
		<div class="space-y-4">
			<h3 class="text-xl font-medium text-gray-800 dark:text-gray-200">Logger Demo</h3>
			<LoggerDemo />
		</div>
	</section>
</div>
