# Svelte 5 SPA Template

## True Single Page Application (SPA) with Svelte 5 + SvelteKit + shadcn-svelte

> ⚠️ **IMPORTANT**: This is a **TRUE SPA APPLICATION** with <PERSON> completely disabled. All rendering happens client-side in the browser.

A modern, production-ready Single Page Application template built with the latest technologies and best practices for client-side development.

## 🚀 SPA Features

- **True SPA Architecture** - 100% client-side rendering, no SSR
- **Svelte 5** with runes for reactive state management in the browser
- **SvelteKit** configured as a Single Page Application with static adapter
- **shadcn-svelte** for beautiful, accessible UI components
- **TailwindCSS 4.0** for utility-first styling
- **TypeScript** for type safety in browser environments
- **Vitest** with browser mode for client-side unit testing
- **Playwright** for end-to-end SPA testing
- **ESLint + Prettier** for code quality
- **Pre-commit hooks** for automated quality checks
- **Static Deployment** - builds to static files for any hosting/CDN
- **Performance Optimized** - bundle analysis, compression, lazy loading
- **VS Code** configuration for optimal SPA development experience

## 📦 SPA Tech Stack

- [Svelte 5](https://svelte.dev/) - Reactive framework with runes for client-side state
- [SvelteKit](https://kit.svelte.dev/) - Configured as SPA with static adapter
- [shadcn-svelte](https://shadcn-svelte.com/) - Accessible component library for SPAs
- [TailwindCSS 4.0](https://tailwindcss.com/) - Utility-first CSS framework
- [TypeScript](https://www.typescriptlang.org/) - Type safety for browser code
- [Vite](https://vitejs.dev/) - Build tool optimized for SPA development
- [@sveltejs/adapter-static](https://svelte.dev/docs/kit/adapter-static) - Static site generation for SPAs

## 🏗️ SPA Architecture

This project is explicitly configured as a **True Single Page Application**:

### Key SPA Configuration

```typescript
// src/routes/+layout.ts - Disables SSR completely
export const ssr = false; // No server-side rendering
export const prerender = false; // No prerendering
```

```javascript
// svelte.config.js - Static adapter with SPA fallback
adapter: adapter({
  fallback: 'index.html'  // All routes fallback to index.html
}),
prerender: {
  entries: []  // No prerendering for true SPA
}
```

### SPA Characteristics

- **Client-Side Only**: All rendering happens in the browser
- **Static Files**: Builds to static HTML, CSS, and JS files
- **Client-Side Routing**: SvelteKit handles navigation without page reloads
- **Browser State**: All state management happens client-side with Svelte 5 runes
- **CDN Ready**: Can be deployed to any static hosting or CDN
- **No Server Required**: Production runs without Node.js server

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+
- npm, pnpm, yarn, or bun

### Installation

1. Clone this repository or create a new project:

```bash
npx sv create my-app
cd my-app
```

2. Install dependencies:

```bash
npm install
```

3. Initialize shadcn-svelte:

```bash
npx shadcn-svelte@latest init
```

4. Start the development server:

```bash
npm run dev
```

5. Open [http://localhost:5173](http://localhost:5173) in your browser.

### 🔧 Pre-commit Setup (Recommended)

This project includes pre-commit hooks for automated code quality checks. To set them up:

1. Install pre-commit (if not already installed):

```bash
# macOS with Homebrew
brew install pre-commit

# Or with pip
pip install pre-commit
```

2. Install the git hooks:

```bash
pre-commit install
pre-commit install --hook-type commit-msg
```

3. (Optional) Run on all files to check current state:

```bash
pre-commit run --all-files
```

The hooks will now run automatically on each commit, checking for:

- Code formatting (Prettier)
- Linting issues (ESLint)
- Security vulnerabilities (Gitleaks)
- File quality (trailing whitespace, large files, etc.)
- Configuration validation (JSON/YAML syntax)

For detailed information, see [PRE_COMMIT_SETUP.md](./docs/PRE_COMMIT_SETUP.md).

## 📁 SPA Project Structure

```
src/
├── lib/
│   ├── components/
│   │   ├── ui/           # shadcn-svelte components
│   │   ├── examples/     # Example SPA components
│   │   └── dev/          # Development tools (performance monitor)
│   ├── utils.ts          # Browser utility functions
│   └── index.ts          # Library exports
├── routes/               # SvelteKit SPA routes
│   ├── +layout.ts        # SPA configuration (SSR disabled)
│   ├── +layout.svelte    # Root layout component
│   └── +page.svelte      # Home page component
├── app.html             # SPA HTML template
├── app.css              # Global styles
└── vitest-setup-client.ts # Browser test setup
```

### Key SPA Files

- **`src/routes/+layout.ts`** - Disables SSR for true SPA behavior
- **`src/app.html`** - Single HTML template for the entire application
- **`svelte.config.js`** - Static adapter configuration with SPA fallback
- **`vite.config.ts`** - Build configuration optimized for SPA deployment

## 🧩 Component Examples

This project includes several example components demonstrating best practices:

### UserCard Component

- Demonstrates Svelte 5 runes (`$state`, `$derived`)
- Proper TypeScript interfaces
- Accessible design with keyboard navigation
- Loading states and event handling

### TaskManager Component

- Complex state management with arrays
- Filtering and derived state
- Form handling and validation
- CRUD operations

### FormExample Component

- Real-time form validation
- Error handling and display
- Accessibility features
- TypeScript form interfaces

## 🎨 Adding Components

### Using shadcn-svelte CLI

```bash
# Add individual components
npm run shadcn:add button
npm run shadcn:add card
npm run shadcn:add input

# Add multiple components
npx shadcn-svelte@latest add button card input label
```

### Creating Custom Components

1. Create your component in `src/lib/components/`
2. Use TypeScript interfaces for props
3. Implement proper accessibility
4. Follow the established patterns from examples

Example component structure:

```svelte
<script lang="ts" module>
	export interface MyComponentProps {
		// Define your props interface
	}
</script>

<script lang="ts">
	import { cn } from '$lib/utils.js';

	let {
		// Destructure props with defaults
	}: MyComponentProps = $props();

	// Use Svelte 5 runes for state
	let state = $state(initialValue);
	let derived = $derived(computation);
</script>

<!-- Your component template -->
```

## 🧪 SPA Testing

This project uses browser-based testing to match the SPA runtime environment.

### Browser-Based Unit Tests

Tests run in real browsers using Vitest browser mode:

```bash
# Run unit tests in browser
npm run test:unit

# Run tests in watch mode with browser
npm run test:unit:watch

# Run tests with UI in browser
npm run test:unit:ui

# Run tests with coverage
npm run test:unit:coverage
```

**Why Browser Testing?** Since this is a true SPA, tests run in the same environment as production code - the browser. This ensures:

- DOM APIs work correctly
- Browser-specific behavior is tested
- Client-side routing functions properly
- Component rendering matches production

### End-to-End SPA Tests

E2E tests validate the complete SPA user experience:

```bash
# Run e2e tests against built SPA
npm run test:e2e

# Run e2e tests with UI
npm run test:e2e:ui
```

E2E tests build the SPA and test it as users would experience it - as a static site with client-side navigation.

## 🔧 Development Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build

# Performance Analysis
npm run analyze      # Generate bundle analysis
npm run analyze:open # Generate and open bundle analysis
npm run perf:build   # Build + analyze performance

# Code Quality
npm run check        # Type check
npm run check:watch  # Type check in watch mode
npm run lint         # Lint code
npm run lint:fix     # Fix linting issues
npm run format       # Format code
npm run format:check # Check formatting
npm run ci           # Run all checks (CI pipeline)
```

## 📝 Code Quality

This project enforces code quality through:

- **ESLint** for code linting
- **Prettier** for code formatting
- **TypeScript** for type checking
- **svelte-check** for Svelte-specific checks
- **Husky** for git hooks (optional)

## 🎯 Best Practices

### Component Design

- Use TypeScript interfaces for all props
- Implement proper accessibility (ARIA labels, keyboard navigation)
- Use Svelte 5 runes for state management
- Follow the single responsibility principle
- Make components reusable and composable

### State Management

- Use `$state` for reactive variables
- Use `$derived` for computed values
- Use `$effect` for side effects
- Keep state as local as possible
- Use stores for global state when needed

### Styling

- Use TailwindCSS utility classes
- Use the `cn()` utility for conditional classes
- Follow the design system from shadcn-svelte
- Use CSS custom properties for theming

### Performance

This project includes comprehensive performance optimizations:

- **Bundle Analysis**: `npm run analyze` for bundle size analysis
- **Compression**: Gzip + Brotli compression for all assets
- **Lazy Loading**: Optimized image loading with intersection observer
- **Code Splitting**: Intelligent vendor chunk splitting
- **Performance Monitoring**: Real-time Core Web Vitals tracking (dev mode)
- **Performance Budgets**: Build-time warnings for large bundles

See [PERFORMANCE_OPTIMIZATIONS.md](./docs/PERFORMANCE_OPTIMIZATIONS.md) for detailed information.

## 🚀 SPA Deployment

This SPA builds to static files and can be deployed anywhere that serves static content.

### Build Static Files

```bash
npm run build
```

This creates a `build/` directory with static HTML, CSS, and JavaScript files.

### Deploy to Static Hosting

#### Vercel (Recommended for SPAs)

```bash
npm i -g vercel
vercel
```

#### Netlify

```bash
npm run build
# Upload build/ folder to Netlify
# Or connect your Git repository for automatic deployments
```

#### GitHub Pages

```bash
npm run build
# Upload build/ folder to gh-pages branch
```

#### Any Static Host

The `build/` folder contains all files needed for deployment:

- Upload to any web server or CDN
- Ensure server redirects all routes to `index.html` for SPA routing
- No server-side configuration required

### SPA Routing Configuration

For proper SPA routing, configure your hosting to:

1. Serve `index.html` for all routes (fallback routing)
2. Set proper MIME types for `.js` and `.css` files
3. Enable gzip/brotli compression for better performance

Example nginx configuration:

```nginx
location / {
  try_files $uri $uri/ /index.html;
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run the CI checks: `npm run ci`
5. Submit a pull request

## 📚 Documentation

For detailed documentation, see the [docs](./docs/) folder:

- **[SPA Architecture Guide](./docs/SPA_ARCHITECTURE.md)** ⭐ - Complete SPA architecture reference
- [Performance Optimizations](./docs/PERFORMANCE_OPTIMIZATIONS.md) - SPA performance guide
- [Best Practices](./docs/BEST_PRACTICES.md) - SPA development patterns
- [Pre-commit Setup](./docs/PRE_COMMIT_SETUP.md) - Code quality automation
- [Docker Guide](./docs/DOCKER.md) - SPA containerization and deployment

## 📚 External Resources

- [Svelte 5 Documentation](https://svelte.dev/docs/svelte/introduction)
- [SvelteKit Documentation](https://kit.svelte.dev/docs)
- [shadcn-svelte Documentation](https://shadcn-svelte.com/docs)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)

## 📄 License

MIT License - see the [LICENSE](LICENSE) file for details.
