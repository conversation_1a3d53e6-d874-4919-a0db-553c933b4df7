# Logging System Documentation

This document describes the standardized logging system implemented in this SPA using `loglevel`.

## Overview

The logging system provides:
- **Level-based logging** with 6 levels (TRACE, DEBUG, INFO, WARN, ERROR, SILENT)
- **Environment-specific configuration** (development vs production)
- **Specialized loggers** for different application modules
- **Performance monitoring** capabilities
- **Error handling** with stack traces
- **Browser-optimized** formatting and colors
- **Persistent log level** settings

## Quick Start

### Basic Usage

```typescript
import { logger } from '$lib';

// Basic logging
logger.info('Application started');
logger.warn('This is a warning');
logger.error('Something went wrong');
logger.debug('Debug information');
logger.trace('Very detailed trace info');
```

### Creating Specialized Loggers

```typescript
import { getLogger } from '$lib';

// Create loggers for different modules
const apiLogger = getLogger('API');
const uiLogger = getLogger('UI');
const authLogger = getLogger('Auth');

apiLogger.info('Making request to /api/users');
uiLogger.debug('Component state updated');
authLogger.warn('Token expires in 5 minutes');
```

### Error Handling

```typescript
import { logger } from '$lib';

try {
  // Some operation that might fail
  await riskyOperation();
} catch (error) {
  // Log the error with context
  logger.exception(error as Error, 'Failed to perform risky operation', { userId: 123 });
}
```

### Performance Monitoring

```typescript
import { logger } from '$lib';

// Start timing
logger.time('data-processing');

// Do some work
await processLargeDataset();

// End timing (will log the duration)
logger.timeEnd('data-processing');
```

### Grouped Logging

```typescript
import { logger } from '$lib';

logger.group('User Registration Process');
logger.info('Step 1: Validating input');
logger.info('Step 2: Creating account');
logger.info('Step 3: Sending welcome email');
logger.groupEnd();
```

## Log Levels

| Level | Numeric Value | When to Use |
|-------|---------------|-------------|
| TRACE | 0 | Very detailed debugging information |
| DEBUG | 1 | General debugging information |
| INFO  | 2 | General application flow information |
| WARN  | 3 | Potentially harmful situations |
| ERROR | 4 | Error events that need attention |
| SILENT| 5 | Disable all logging |

### Setting Log Levels

```typescript
import { logger, LOG_LEVELS } from '$lib';

// Set specific level
logger.setLevel('DEBUG');

// Or use constants
logger.setLevel(LOG_LEVELS.DEBUG);

// Enable all logging
logger.enableAll(); // Sets to TRACE

// Disable all logging
logger.disableAll(); // Sets to SILENT
```

## Environment Configuration

### Development Environment
- **Default Level**: DEBUG
- **Timestamp**: Enabled
- **Colors**: Enabled
- **Persistence**: Enabled

### Production Environment
- **Default Level**: WARN
- **Timestamp**: Disabled
- **Colors**: Disabled
- **Persistence**: Disabled

### URL Parameter Override

You can override the log level using URL parameters:
```
https://yourapp.com?logLevel=DEBUG
```

### localStorage Override

The log level can be persisted in localStorage and will be restored on page reload.

## Best Practices

### 1. Use Appropriate Log Levels

```typescript
// ✅ Good
logger.info('User logged in successfully');
logger.warn('API response took 3.2s (threshold: 2s)');
logger.error('Failed to save user preferences');

// ❌ Bad
logger.error('User logged in'); // Not an error
logger.info('Critical system failure'); // Should be error
```

### 2. Include Context

```typescript
// ✅ Good
logger.info('User authentication successful', { userId: 123, method: 'oauth' });
logger.error('Database connection failed', { host: 'db.example.com', timeout: 30000 });

// ❌ Bad
logger.info('Success');
logger.error('Failed');
```

### 3. Use Specialized Loggers

```typescript
// ✅ Good
const apiLogger = getLogger('API');
const uiLogger = getLogger('UI');

apiLogger.debug('Request sent to /api/users');
uiLogger.debug('Modal component opened');

// ❌ Less ideal
logger.debug('API: Request sent to /api/users');
logger.debug('UI: Modal component opened');
```

### 4. Handle Errors Properly

```typescript
// ✅ Good
try {
  await apiCall();
} catch (error) {
  logger.exception(error as Error, 'API call failed', { endpoint: '/users' });
}

// ❌ Bad
try {
  await apiCall();
} catch (error) {
  logger.error('Error occurred');
}
```

### 5. Avoid Logging Sensitive Information

```typescript
// ✅ Good
logger.info('User authentication successful', { userId: user.id });

// ❌ Bad - Never log sensitive data
logger.debug('User login', { password: user.password, token: jwt });
```

## Advanced Features

### Creating Custom Loggers

```typescript
import { createLogger } from '$lib';

const customLogger = createLogger({
  level: 'INFO',
  prefix: 'MyModule',
  enableTimestamp: true,
  enableColors: false
});
```

### Child Loggers

```typescript
const parentLogger = getLogger('Parent');
const childLogger = parentLogger.child('Child');

childLogger.info('This will show as [Parent:Child] message');
```

## Testing

When writing tests, you can mock the logger:

```typescript
import { logger } from '$lib';

// In your test setup
beforeEach(() => {
  // Mock all logger methods
  vi.spyOn(logger, 'info').mockImplementation(() => {});
  vi.spyOn(logger, 'error').mockImplementation(() => {});
  // ... mock other methods as needed
});
```

## Debugging

### Enable Verbose Logging

In development, you can enable all logging:

```typescript
import { logger } from '$lib';

// Enable all logging levels
logger.enableAll();
```

### Check Current Log Level

```typescript
import { logger } from '$lib';

console.log('Current log level:', logger.getLevel());
```

### Browser Console

All logs are output to the browser console with appropriate formatting and colors in development mode.

## Performance Considerations

- Log level filtering happens at the library level, so disabled log levels have minimal performance impact
- In production, only WARN and ERROR levels are logged by default
- Performance logging (`time`/`timeEnd`) is automatically disabled in production
- String concatenation in log messages is only performed if the log level is enabled

## Migration from console.log

Replace existing console.log statements:

```typescript
// Before
console.log('User data loaded');
console.warn('API deprecated');
console.error('Network error');

// After
import { logger } from '$lib';

logger.info('User data loaded');
logger.warn('API deprecated');
logger.error('Network error');
```

## Troubleshooting

### Logs Not Appearing

1. Check the current log level: `logger.getLevel()`
2. Ensure you're using the correct log level for your message
3. Check browser console for any JavaScript errors
4. Verify the logger is properly imported

### Performance Issues

1. Avoid expensive operations in log messages
2. Use appropriate log levels (avoid TRACE in production)
3. Consider using conditional logging for expensive debug information

### Configuration Issues

1. Check environment variables and URL parameters
2. Verify localStorage settings
3. Ensure proper import paths for configuration files
