# 🏗️ SPA Architecture Guide

**Complete guide to the Single Page Application architecture of this Svelte 5 template**

> ⚠️ **CRITICAL**: This is a **TRUE SPA APPLICATION** with <PERSON> completely disabled. All rendering happens client-side in the browser.

## 🎯 SPA Overview

This project is a production-ready Single Page Application (SPA) template that:

- **Runs entirely in the browser** - No server-side rendering
- **Builds to static files** - Can be deployed to any static hosting
- **Uses client-side routing** - Navigation without page reloads
- **Manages state in the browser** - Using Svelte 5 runes
- **Optimized for performance** - Bundle splitting, compression, lazy loading

## 🔧 SPA Configuration

### 1. SSR Disabled

```typescript
// src/routes/+layout.ts
export const ssr = false; // Disable server-side rendering
export const prerender = false; // Disable prerendering
```

This configuration ensures:

- All components render in the browser only
- No server-side code execution
- True client-side application behavior

### 2. Static Adapter Configuration

```javascript
// svelte.config.js
import adapter from '@sveltejs/adapter-static';

const config = {
	kit: {
		adapter: adapter({
			fallback: 'index.html' // SPA fallback for client-side routing
		}),
		prerender: {
			entries: [] // No prerendering for SPA
		}
	}
};
```

This setup:

- Builds to static HTML, CSS, and JS files
- Configures fallback routing for SPA navigation
- Eliminates server-side dependencies

### 3. Vite SPA Optimizations

```typescript
// vite.config.ts
export default defineConfig({
	build: {
		rollupOptions: {
			output: {
				manualChunks: (id) => {
					// Intelligent chunk splitting for SPA
					if (id.includes('node_modules')) {
						if (id.includes('svelte')) return 'vendor-svelte';
						if (id.includes('bits-ui') || id.includes('tailwind')) return 'vendor-ui';
						return 'vendor';
					}
				}
			}
		}
	},
	test: {
		browser: {
			enabled: true, // Browser-based testing for SPA
			provider: 'playwright'
		}
	}
});
```

## 🌐 Client-Side Architecture

### State Management with Svelte 5 Runes

```typescript
// Example component with client-side state
<script lang="ts">
  // Reactive state in the browser
  let count = $state(0);

  // Derived state computed in browser
  let doubled = $derived(count * 2);

  // Side effects in browser environment
  $effect(() => {
    console.log('Count changed:', count);
  });
</script>
```

### Client-Side Routing

```typescript
// SvelteKit handles routing in the browser
// All navigation is client-side, no page reloads
// Routes are defined in src/routes/ directory
```

### Browser-Only APIs

```typescript
// Safe to use browser APIs since SSR is disabled
import { onMount } from 'svelte';

onMount(() => {
	// Browser-only code
	const data = localStorage.getItem('user-data');
	console.log('Window size:', window.innerWidth);
});
```

## 📦 Build Output

The SPA builds to a `build/` directory containing:

```
build/
├── index.html              # Single HTML file for entire app
├── _app/
│   ├── immutable/
│   │   ├── chunks/         # Code-split chunks
│   │   ├── assets/         # CSS and other assets
│   │   └── entry/          # Application entry points
│   └── version.json        # Build version info
└── favicon.svg             # Static assets
```

### Key Build Characteristics

- **Single HTML File**: `index.html` serves all routes
- **Code Splitting**: Intelligent chunking for optimal loading
- **Static Assets**: All resources are static files
- **No Server Dependencies**: Runs on any web server or CDN

## 🚀 Deployment Architecture

### Static Hosting Requirements

1. **Serve Static Files**: Host can serve HTML, CSS, JS files
2. **Fallback Routing**: All routes should serve `index.html`
3. **MIME Types**: Proper content types for assets
4. **Compression**: Gzip/Brotli for performance

### Example Hosting Configurations

#### Nginx

```nginx
server {
  location / {
    try_files $uri $uri/ /index.html;
  }
}
```

#### Apache

```apache
RewriteEngine On
RewriteRule ^(?!.*\.).*$ /index.html [L]
```

#### Vercel (vercel.json)

```json
{
	"rewrites": [{ "source": "/(.*)", "destination": "/index.html" }]
}
```

## 🔍 Development vs Production

### Development Mode

- Vite dev server with HMR
- Source maps for debugging
- Performance monitoring tools
- Browser-based testing

### Production Mode

- Static file generation
- Minified and compressed assets
- Optimized bundle splitting
- CDN-ready deployment

## 🧪 Testing Strategy

### Browser-Based Unit Tests

- Tests run in real browsers (Playwright)
- DOM APIs available during testing
- Component rendering matches production
- Client-side routing testable

### End-to-End Testing

- Tests against built static files
- Validates complete SPA user experience
- Ensures proper fallback routing
- Performance testing possible

## 📊 Performance Characteristics

### SPA-Specific Optimizations

- **Bundle Analysis**: Monitor client-side bundle size
- **Code Splitting**: Lazy load routes and components
- **Asset Optimization**: Compress images and fonts
- **Caching Strategy**: Leverage browser caching for chunks
- **Core Web Vitals**: Monitor browser performance metrics

### Performance Monitoring

```typescript
// Built-in performance monitoring for development
// Tracks Core Web Vitals in browser
// Bundle size analysis tools included
```

## 🔒 Security Considerations

### Client-Side Security

- All code runs in browser (public)
- No server-side secrets
- CSP headers recommended
- XSS protection important
- Secure external API communication

### Best Practices

- Validate all user inputs
- Sanitize dynamic content
- Use HTTPS for API calls
- Implement proper authentication flows
- Regular dependency updates

## 🎯 LLM Integration Notes

### Key Points for AI Understanding

1. **True SPA**: No server-side rendering, browser-only execution
2. **Static Deployment**: Builds to files that work on any web server
3. **Modern Stack**: Svelte 5 runes, SvelteKit routing, TypeScript
4. **Performance Focused**: Optimized for client-side performance
5. **Developer Experience**: Full tooling for SPA development

### Common SPA Patterns

- State management with `$state`, `$derived`, `$effect`
- Client-side navigation with SvelteKit router
- Component composition with shadcn-svelte
- Browser testing with Vitest browser mode
- Static deployment to CDNs and hosting providers
