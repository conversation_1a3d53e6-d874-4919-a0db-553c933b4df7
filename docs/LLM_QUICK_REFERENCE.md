# 🤖 LLM Quick Reference

**Essential information for Large Language Models working with this Svelte 5 SPA template**

## ⚠️ CRITICAL: This is a TRUE SPA APPLICATION

- **NO SERVER-SIDE RENDERING** - All code runs in the browser
- **STATIC DEPLOYMENT** - Builds to HTML/CSS/JS files only
- **CLIENT-SIDE ROUTING** - Navigation without page reloads
- **BROWSER-ONLY STATE** - All state management in client

## 🏗️ Architecture Summary

### SPA Configuration

```typescript
// src/routes/+layout.ts
export const ssr = false; // SSR disabled
export const prerender = false; // No prerendering
```

```javascript
// svelte.config.js
adapter: adapter({ fallback: 'index.html' }); // SPA fallback
```

### Tech Stack

- **Svelte 5** - Client-side reactive framework with runes
- **SvelteKit** - Configured as SPA with static adapter
- **shadcn-svelte** - UI component library
- **TailwindCSS 4.0** - Utility-first CSS
- **TypeScript** - Type safety for browser code
- **Vite** - Build tool for SPA optimization

## 🔧 Key Patterns

### State Management (Svelte 5 Runes)

```typescript
// Client-side reactive state
let count = $state(0);
let doubled = $derived(count * 2);

$effect(() => {
	// Browser-only side effects
	localStorage.setItem('count', count.toString());
});
```

### Component Structure

```svelte
<script lang="ts" module>
	export interface Props {
		// TypeScript interfaces
	}
</script>

<script lang="ts">
	import { cn } from '$lib/utils.js';

	let { prop1, prop2 = 'default' }: Props = $props();

	// Browser-only code is safe
	let browserState = $state(window.innerWidth);
</script>
```

### Safe Browser APIs

```typescript
import { onMount } from 'svelte';

onMount(() => {
	// All browser APIs are available
	const data = localStorage.getItem('key');
	console.log('Window:', window.location.href);
});
```

## 📁 Project Structure

```
src/
├── routes/
│   ├── +layout.ts        # SSR disabled
│   ├── +layout.svelte    # Root layout
│   └── +page.svelte      # Home page
├── lib/
│   ├── components/ui/    # shadcn-svelte components
│   └── utils.ts          # Browser utilities
├── app.html             # SPA template
└── app.css              # Global styles
```

## 🚀 Build & Deployment

### Build Output

```bash
npm run build  # Creates build/ directory with static files
```

### Deployment

- **Static hosting** - Any web server or CDN
- **Fallback routing** - All routes serve `index.html`
- **No server required** - Pure static files

### Popular Hosts

- Vercel, Netlify, GitHub Pages
- Any CDN or static hosting
- Nginx, Apache with fallback config

## 🧪 Testing

### Browser-Based Tests

```bash
npm run test:unit  # Vitest with browser mode
npm run test:e2e   # Playwright E2E tests
```

Tests run in real browsers to match SPA runtime.

## 📊 Performance

### SPA Optimizations

- Bundle analysis: `npm run analyze`
- Code splitting for client-side chunks
- Compression (gzip/brotli)
- Lazy loading components
- Core Web Vitals monitoring

### Key Scripts

```bash
npm run dev          # Development server
npm run build        # Build static SPA
npm run preview      # Preview built SPA
npm run analyze      # Bundle analysis
npm run perf:build   # Build + performance analysis
```

## 🔒 Security Notes

- All code is public (runs in browser)
- No server-side secrets possible
- Client-side validation only
- Secure API communication required
- CSP headers recommended

## 🎯 Common SPA Patterns

### Routing

```typescript
// Client-side navigation
import { goto } from '$app/navigation';
goto('/path'); // No page reload
```

### Data Loading

```typescript
// Browser-only data fetching
onMount(async () => {
	const response = await fetch('/api/data');
	const data = await response.json();
});
```

### State Persistence

```typescript
// Browser storage
let settings = $state(JSON.parse(localStorage.getItem('settings') || '{}'));

$effect(() => {
	localStorage.setItem('settings', JSON.stringify(settings));
});
```

## 📚 Documentation Structure

1. **[SPA_ARCHITECTURE.md](./SPA_ARCHITECTURE.md)** - Complete architecture guide
2. **[PERFORMANCE_OPTIMIZATIONS.md](./PERFORMANCE_OPTIMIZATIONS.md)** - SPA performance
3. **[BEST_PRACTICES.md](./BEST_PRACTICES.md)** - SPA development patterns
4. **[DOCKER.md](./DOCKER.md)** - Containerized SPA development
5. **[PRE_COMMIT_SETUP.md](./PRE_COMMIT_SETUP.md)** - Code quality automation

## 🚨 Important Reminders for LLMs

1. **Always assume browser environment** - No Node.js server in production
2. **Use client-side patterns** - State, routing, data fetching
3. **Static deployment focus** - All solutions must work with static files
4. **Browser testing** - Tests run in real browsers
5. **Performance matters** - Bundle size affects loading speed
6. **Security awareness** - All code is public in browser

## 🔗 Quick Links

- Main README: [`../README.md`](../README.md)
- SvelteKit SPA docs: https://svelte.dev/docs/kit/adapter-static
- Svelte 5 runes: https://svelte.dev/docs/svelte/introduction
- shadcn-svelte: https://shadcn-svelte.com/
