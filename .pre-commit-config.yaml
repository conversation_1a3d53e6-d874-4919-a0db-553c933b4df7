# Pre-commit configuration for Svelte 5 + shadcn-svelte project
# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

default_language_version:
  python: python3
  node: '20'

repos:
  # Core pre-commit hooks for general file quality
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        name: Trim trailing whitespace
      - id: end-of-file-fixer
        name: Fix end of files
      - id: check-yaml
        name: Check YAML syntax
      - id: check-json
        name: Check JSON syntax
      - id: check-toml
        name: Check TOML syntax
      - id: check-merge-conflict
        name: Check for merge conflicts
      - id: check-added-large-files
        name: Check for large files
        args: ['--maxkb=1000']
      - id: mixed-line-ending
        name: Check line endings
        args: ['--fix=lf']
      - id: check-case-conflict
        name: Check for case conflicts
      - id: check-symlinks
        name: Check for broken symlinks

  # Modern JavaScript/TypeScript linting and formatting with Biome (local)
  # Note: Using local hook to avoid Node.js environment issues

  # Security: Secret detection with Gitleaks
  - repo: https://github.com/gitleaks/gitleaks
    rev: v8.28.0
    hooks:
      - id: gitleaks
        name: Detect secrets with Gitleaks

  # Text quality: Fix common typos
  - repo: https://github.com/crate-ci/typos
    rev: v1.34.0
    hooks:
      - id: typos
        name: Fix typos
        args: ['--write-changes']

  # JSON Schema validation for package.json and other config files
  - repo: https://github.com/python-jsonschema/check-jsonschema
    rev: 0.33.2
    hooks:
      - id: check-jsonschema
        name: Validate package.json
        files: ^package\.json$
        args: ['--schemafile', 'https://json.schemastore.org/package.json']
      - id: check-jsonschema
        name: Validate tsconfig.json
        files: ^tsconfig.*\.json$
        args: ['--schemafile', 'https://json.schemastore.org/tsconfig.json']

  # Local hooks for project-specific tools
  - repo: local
    hooks:
      # ESLint with existing configuration
      - id: eslint
        name: ESLint
        entry: npx eslint
        language: system
        types: [file]
        files: \.(js|ts|svelte)$
        args: ['--fix']
        require_serial: false

      # Prettier with existing configuration
      - id: prettier
        name: Prettier
        entry: npx prettier
        language: system
        types: [file]
        files: \.(js|ts|svelte|json|yaml|yml|md|css|scss|html)$
        args: ['--write']
        require_serial: false

# Configuration for specific hooks
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
