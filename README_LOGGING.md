# Standardized Logging Implementation

This project now includes a comprehensive, standardized logging solution using `loglevel` optimized for Single Page Applications (SPAs).

## 🎯 What Was Implemented

### Core Logging System
- **Library**: `loglevel` - lightweight, browser-optimized logging library (1.4KB gzipped)
- **Enhanced Logger**: Custom wrapper with additional features for SPA development
- **Environment Configuration**: Automatic configuration based on development/production environments
- **TypeScript Support**: Full type definitions and IntelliSense support

### Key Features
✅ **Level-based Logging**: TRACE, DEBUG, INFO, WARN, ERROR, SILENT  
✅ **Specialized Loggers**: Create module-specific loggers with prefixes  
✅ **Environment Awareness**: Different log levels for dev/prod  
✅ **Performance Monitoring**: Built-in timing and performance logging  
✅ **Error Handling**: Enhanced exception logging with stack traces  
✅ **Browser Optimized**: Maintains proper stack traces and console formatting  
✅ **Persistent Settings**: Log level preferences saved in localStorage  
✅ **URL Override**: Change log level via URL parameters  

## 📁 File Structure

```
src/lib/
├── logger.ts                 # Main logger implementation
├── config/
│   └── logging.ts            # Environment-specific configuration
├── types/
│   └── logger.ts             # TypeScript type definitions
└── index.ts                  # Exports for easy importing

docs/
└── LOGGING.md                # Comprehensive documentation

src/lib/components/examples/
└── LoggerDemo.svelte         # Interactive demo component
```

## 🚀 Quick Start

### Basic Usage

```typescript
import { logger } from '$lib';

// Basic logging
logger.info('Application started');
logger.warn('This is a warning');
logger.error('Something went wrong');
logger.debug('Debug information');
```

### Specialized Loggers

```typescript
import { getLogger } from '$lib';

const apiLogger = getLogger('API');
const uiLogger = getLogger('UI');

apiLogger.info('Making request to /api/users');
uiLogger.debug('Component state updated');
```

### Error Handling

```typescript
try {
  await riskyOperation();
} catch (error) {
  logger.exception(error as Error, 'Operation failed', { context: 'additional info' });
}
```

## 🎛️ Configuration

### Environment-Based Settings

| Environment | Default Level | Timestamp | Colors | Persistence |
|-------------|---------------|-----------|--------|-------------|
| Development | DEBUG         | ✅        | ✅     | ✅          |
| Production  | WARN          | ❌        | ❌     | ❌          |

### Override Options

1. **URL Parameter**: `?logLevel=DEBUG`
2. **localStorage**: Automatically persisted in development
3. **Programmatic**: `logger.setLevel('TRACE')`

## 🧪 Testing the Implementation

### 1. Start Development Server
```bash
npm run dev
```

### 2. Open Browser Console
Navigate to `http://localhost:5173` and open browser developer tools.

### 3. Interactive Demo
The main page includes a "Logger Demo" section with buttons to test all logging features.

### 4. Real-World Examples
- **TaskManager Component**: Shows logging in action when adding/toggling tasks
- **Main Page**: Logs SPA initialization

## 📊 Log Levels Explained

| Level | When to Use | Example |
|-------|-------------|---------|
| TRACE | Very detailed debugging | `logger.trace('Function entry', { params })` |
| DEBUG | General debugging | `logger.debug('Processing user data')` |
| INFO  | General information | `logger.info('User logged in successfully')` |
| WARN  | Potential issues | `logger.warn('API response slow: 3.2s')` |
| ERROR | Actual errors | `logger.error('Failed to save data')` |

## 🔧 Advanced Features

### Performance Monitoring
```typescript
logger.time('data-processing');
// ... do work ...
logger.timeEnd('data-processing'); // Logs duration
```

### Grouped Logging
```typescript
logger.group('User Registration');
logger.info('Step 1: Validation');
logger.info('Step 2: Account creation');
logger.groupEnd();
```

### Child Loggers
```typescript
const parentLogger = getLogger('Parent');
const childLogger = parentLogger.child('Child');
// Logs will show as [Parent:Child]
```

## 🎨 Browser Console Output

In development mode, logs appear with:
- **Timestamps**: HH:mm:ss.SSS format
- **Colors**: Level-based color coding
- **Prefixes**: Module/component identification
- **Proper Stack Traces**: Points to your code, not the logger

Example output:
```
12:34:56.789 [TaskManager] [INFO] Task added successfully {taskId: 1234, title: "New Task"}
12:34:56.790 [TaskManager] [DEBUG] Total tasks after addition: 5
```

## 🔄 Migration from console.log

### Before
```typescript
console.log('User data loaded');
console.warn('API deprecated');
console.error('Network error');
```

### After
```typescript
import { logger } from '$lib';

logger.info('User data loaded');
logger.warn('API deprecated');
logger.error('Network error');
```

## 📈 Benefits Over console.log

1. **Level Control**: Filter logs by importance
2. **Environment Awareness**: Automatic prod/dev behavior
3. **Structured Logging**: Consistent format and context
4. **Performance**: Disabled logs have zero overhead
5. **Debugging**: Better stack traces and grouping
6. **Persistence**: Remember log level preferences
7. **Modularity**: Separate loggers for different components

## 🛠️ Best Practices

### ✅ Do
- Use appropriate log levels
- Include relevant context in messages
- Create specialized loggers for modules
- Use structured data in log messages
- Handle errors with `logger.exception()`

### ❌ Don't
- Log sensitive information (passwords, tokens)
- Use console.log directly
- Create overly verbose trace logs
- Log in tight loops without level checks
- Ignore error context

## 🔍 Debugging Tips

1. **Enable All Logs**: `logger.enableAll()` in console
2. **Check Current Level**: `logger.getLevel()`
3. **URL Override**: Add `?logLevel=TRACE` to URL
4. **Module-Specific**: Use specialized loggers to filter by component

## 📚 Documentation

For complete documentation, see [docs/LOGGING.md](docs/LOGGING.md)

## 🎯 Next Steps

The logging system is now ready for production use. Consider:

1. **Remote Logging**: Add HTTP transport for production error tracking
2. **Log Aggregation**: Integrate with services like LogRocket, Sentry, or DataDog
3. **Custom Formatters**: Add JSON formatting for structured logging
4. **Performance Metrics**: Extend timing capabilities for performance monitoring

---

**Note**: This implementation provides a solid foundation for logging in SPAs while maintaining excellent performance and developer experience.
