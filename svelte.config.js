import adapter from '@sveltejs/adapter-static';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://svelte.dev/docs/kit/integrations
	// for more information about preprocessors
	preprocess: vitePreprocess(),
	kit: {
		adapter: adapter({
			// Configure fallback page for SPA mode
			fallback: 'index.html'
		}),
		prerender: {
			entries: [] // No prerendering for SPA
		}
	}
};

export default config;
